/**
 * 认证相关工具函数
 * 用于统一处理认证错误和重定向逻辑
 */

import { useUserStore } from '../stores/user'
import { message } from 'ant-design-vue'

// 状态码常量 - 根据状态码.txt文件定义
export const STATUS_CODES = {
  SUCCESS: 200,
  SUCCESS_BIGINT: 1073741824, // 大整数成功状态码，用于某些API
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  BAD_REQUEST: 400,

  // 业务异常码
  USER_NOT_EXIST: 1001,
  LOGIN_USERNAME_PASSWORD_EMPTY: 1002,
  LOGIN_USERNAME_PASSWORD_ERROR: 1004,
  LOGIN_SUCCESS: 1005,
  LOGOUT_SUCCESS: 1006,
  LOGIN_FAIL: 1007,
  LOGOUT_FAIL: 1008,
  NOT_LOGIN: 1009
}

/**
 * 检查是否为认证相关错误
 * @param {Object} error - 错误对象
 * @returns {boolean} 是否为认证错误
 */
export const isAuthError = (error) => {
  // 检查HTTP状态码
  if (error.response && error.response.status === STATUS_CODES.UNAUTHORIZED) {
    return true
  }
  
  // 检查业务状态码
  if (error.response && error.response.data) {
    const { code } = error.response.data
    return code === STATUS_CODES.NOT_LOGIN || code === STATUS_CODES.UNAUTHORIZED
  }
  
  // 检查错误消息中的业务状态码
  if (error.message && error.message.includes('1009')) {
    return true
  }
  
  return false
}

/**
 * 获取当前页面的完整路径（包括查询参数）
 * @param {Object} router - Vue Router实例
 * @returns {string} 当前页面路径
 */
export const getCurrentPath = (router) => {
  if (!router || !router.currentRoute) {
    return window.location.pathname + window.location.search
  }
  return router.currentRoute.value.fullPath
}

/**
 * 清除用户认证信息
 * @param {Object} userStore - 用户store实例
 */
export const clearAuthInfo = async (userStore) => {
  try {
    await userStore.logout()
  } catch (error) {
    console.error('清除认证信息失败:', error)
    // 即使logout失败也要清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('userId')
    localStorage.removeItem('userName')
    localStorage.removeItem('avatar')
    localStorage.removeItem('role')
  }
}

/**
 * 处理认证错误的统一函数
 * @param {Object} error - 错误对象
 * @param {Object} router - Vue Router实例
 * @param {string} currentPath - 当前页面路径（可选，如果不提供会自动获取）
 * @param {Object} options - 配置选项
 * @param {boolean} options.showMessage - 是否显示错误消息（默认true）
 * @param {string} options.messageText - 自定义错误消息
 * @param {boolean} options.clearAuth - 是否清除认证信息（默认true）
 * @returns {Promise<void>}
 */
export const handleAuthError = async (error, router, currentPath = null, options = {}) => {
  const {
    showMessage = true,
    messageText = '登录已过期，请重新登录',
    clearAuth = true
  } = options
  
  // 检查是否为认证错误
  if (!isAuthError(error)) {
    throw error // 如果不是认证错误，重新抛出
  }
  
  console.warn('检测到认证错误:', error)
  
  // 显示错误消息
  if (showMessage) {
    message.warning(messageText)
  }
  
  // 清除认证信息
  if (clearAuth) {
    const userStore = useUserStore()
    await clearAuthInfo(userStore)
  }
  
  // 获取当前路径用于返回
  const returnPath = currentPath || getCurrentPath(router)
  
  // 重定向到登录页
  if (router && router.push) {
    await router.push({
      path: '/login',
      query: { redirect: returnPath }
    })
  } else {
    // 备用方案：使用window.location
    const loginUrl = `/login?redirect=${encodeURIComponent(returnPath)}`
    window.location.href = loginUrl
  }
}

/**
 * 检查用户是否已登录
 * @param {Object} userStore - 用户store实例（可选）
 * @returns {boolean} 是否已登录
 */
export const isUserLoggedIn = (userStore = null) => {
  if (userStore) {
    return userStore.isLoggedIn && userStore.token
  }
  
  // 备用检查：直接检查localStorage
  const token = localStorage.getItem('token')
  const userId = localStorage.getItem('userId')
  return !!(token && userId)
}

/**
 * 在执行需要认证的操作前检查登录状态
 * @param {Object} router - Vue Router实例
 * @param {Object} userStore - 用户store实例（可选）
 * @param {string} currentPath - 当前页面路径（可选）
 * @returns {Promise<boolean>} 是否已登录
 */
export const requireAuth = async (router, userStore = null, currentPath = null) => {
  const store = userStore || useUserStore()
  
  if (!isUserLoggedIn(store)) {
    const returnPath = currentPath || getCurrentPath(router)
    
    message.warning('请先登录')
    
    if (router && router.push) {
      await router.push({
        path: '/login',
        query: { redirect: returnPath }
      })
    } else {
      const loginUrl = `/login?redirect=${encodeURIComponent(returnPath)}`
      window.location.href = loginUrl
    }
    
    return false
  }
  
  return true
}

/**
 * 创建带认证检查的API调用包装器
 * @param {Function} apiCall - API调用函数
 * @param {Object} router - Vue Router实例
 * @param {Object} options - 配置选项
 * @returns {Function} 包装后的API调用函数
 */
export const withAuthCheck = (apiCall, router, options = {}) => {
  return async (...args) => {
    try {
      return await apiCall(...args)
    } catch (error) {
      await handleAuthError(error, router, null, options)
      throw error // 重新抛出错误以便调用方处理
    }
  }
}

/**
 * 从URL查询参数中获取重定向路径
 * @param {Object} route - Vue Router route对象
 * @returns {string} 重定向路径，默认为首页
 */
export const getRedirectPath = (route) => {
  if (!route || !route.query) {
    return '/'
  }
  
  const redirect = route.query.redirect
  
  // 验证重定向路径的安全性
  if (redirect && typeof redirect === 'string') {
    // 确保重定向路径是相对路径，防止开放重定向攻击
    if (redirect.startsWith('/') && !redirect.startsWith('//')) {
      return redirect
    }
  }
  
  return '/'
}

/**
 * 购物车操作专用的认证错误处理
 * @param {Object} error - 错误对象
 * @param {Object} router - Vue Router实例
 * @param {string} operation - 操作名称（用于错误消息）
 * @returns {Promise<void>}
 */
export const handleCartAuthError = async (error, router, operation = '购物车操作') => {
  const options = {
    messageText: `${operation}需要登录，请先登录`,
    showMessage: true,
    clearAuth: true
  }
  
  await handleAuthError(error, router, null, options)
}
