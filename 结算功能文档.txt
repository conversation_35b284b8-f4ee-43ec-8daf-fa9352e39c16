点击购物车中的结算按钮时，跳转至结算页面
结算页面：
	包含3个板块：收货地址选择，确认订单信息，付款详情
	收货地址选择：
		1. 查询接口获取用户已有的收货地址，未查询到则显示空。接口地址(get /address/list)
		2. 可通过点击按钮'使用新地址' 当前页面弹框新增收获地址，新增后刷新收货地址列表。新增接口地址(post /address/add)
	确认订单信息：
		1. 查询接口获取商品结算详情（post /cart/getSettleProductInfo），列表展示商品名称，描述，规格，购买数量，价格（需同时显示原价和折后价）
		2. 订单备注填写(默认为空)
	付款详情：
		1. 通过前面查询到的商品信息展示 商品总价，共减金额，实付款
		2. 支付方式选择，勾选支付宝或者微信（固定这两种, 勾选传参 支付宝(AliPay)、微信(WechatPay)）
		3. 提交订单(功能未完成)
	

接口文档
get /address/list 需要登录后才能调用否则跳转登录
前端参数：无
后端返回: 

{
  "code": 1073741824, //后端状态码
  "message": "string",
  "data": [
    {
      "id": 9007199254740991, // id
      "createdBy": 9007199254740991,
      "createdAt": "2025-08-06T06:16:49.910Z",
      "updatedBy": 9007199254740991,
      "updatedAt": "2025-08-06T06:16:49.910Z",
      "deleted": 1073741824,
      "userId": 9007199254740991, //用户id
      "name": "string", // 收件人名称
      "phone": "string", // 收件人手机号
      "province": "string", // 省
      "city": "string", // 市/县
      "district": "string", // 区/街道
      "detailAddress": "string", // 详细地址
      "postalCode": "string", // 邮政编码
      "isDefault": 1073741824 // 是否默认地址
    }
  ]
}

post /address/add 需登录
前端参数：
{
  "id": 9007199254740991,
  "createdBy": 9007199254740991,
  "createdAt": "2025-08-06T06:22:31.713Z",
  "updatedBy": 9007199254740991,
  "updatedAt": "2025-08-06T06:22:31.713Z",
  "deleted": 1073741824,
  "userId": 9007199254740991,
  "name": "string",
  "phone": "string",
  "province": "string",
  "city": "string",
  "district": "string",
  "detailAddress": "string",
  "postalCode": "string",
  "isDefault": 1
}
后端返回：
{
  "code": 1073741824,
  "message": "string",
  "data": null
}

post /cart/getSettleProductInfo
前端参数：
{
  "cartIds": [1947574100132753409, 1947574139462742017] //选中的购物车项id, 点结算时带过来的
}

后端返回：
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "cartId": 1947574100132753409, // 购物车项id
            "productId": 1937040020379054081, // 商品id
            "quantity": 1, // 数量
            "specModel": "42码", // 规格
            "productName": "女士运动鞋", //商品名称
            "productImage": "http://localhost:8000/shoe1.png", // 商品图片地址
            "description": "透气轻便运动鞋，适合日常跑步和健身。", // 商品描述
            "originalPrice": 599.00, // 原价
            "discountPrice": 499.00  // 折后价
        },
        {
            "cartId": 1947574139462742017,
            "productId": 1937040020379054083,
            "quantity": 4,
            "specModel": "40码",
            "productName": "跑步运动鞋",
            "productImage": "http://localhost:8000/shoe3.png",
            "description": "透气轻便运动鞋，适合日常跑步和健身。",
            "originalPrice": 599.00,
            "discountPrice": 499.00
        }
    ]
}
