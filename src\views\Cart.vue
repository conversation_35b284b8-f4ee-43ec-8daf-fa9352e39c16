<template>
  <div class="cart-page">
    <div class="cart-container">
      <div class="cart-header">
        <h1 class="page-title">购物车</h1>
        <a-button @click="$router.back()" type="text">
          <template #icon>
            <ArrowLeftOutlined />
          </template>
          返回
        </a-button>
      </div>

      <!-- 加载状态 -->
      <div v-if="cartStore.loading" class="loading-container">
        <a-spin size="large" />
        <p>加载中...</p>
      </div>

      <!-- 空购物车 -->
      <a-empty
        v-else-if="!cartStore.items.length"
        description="购物车是空的"
      >
        <template #extra>
          <a-button type="primary" @click="$router.push('/')">
            去购物
          </a-button>
        </template>
      </a-empty>

      <!-- 购物车列表 -->
      <template v-else>
        <div class="cart-list">
          <a-card
            v-for="item in cartItems"
            :key="item.id"
            class="cart-item"
            :loading="cartStore.syncing"
          >
            <div class="item-content">
              <a-checkbox
                v-model:checked="item.selected"
                @change="updateSelection"
              />
              <div class="item-image">
                <a-image
                  :src="item.productImage"
                  :width="80"
                  :height="80"
                  :preview="false"
                  fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                />
              </div>
              <div class="item-info">
                <h3 class="item-name">{{ item.productName }}</h3>
                <div class="item-spec" v-if="item.specModel">规格：{{ item.specModel }}</div>
                <div class="item-price">{{ item.unit }}{{ item.price }}</div>
              </div>
              <div class="item-quantity">
                <a-input-number
                  v-model:value="item.quantity"
                  :min="1"
                  :max="10"
                  @change="(val) => handleQuantityChange(item.id, val)"
                />
              </div>
              <div class="item-total">
                {{ item.unit }}{{ (item.price * item.quantity).toFixed(2) }}
              </div>
              <a-button
                type="text"
                danger
                @click="handleRemoveItem(item.id)"
                class="remove-button"
              >
                <template #icon>
                  <DeleteOutlined />
                </template>
              </a-button>
            </div>
          </a-card>
        </div>

        <div class="cart-footer">
          <div class="select-all">
            <a-checkbox
              v-model:checked="allSelected"
              @change="toggleSelectAll"
            >
              全选
            </a-checkbox>
          </div>
          <div class="cart-summary">
            <div class="total-items">
              已选择 {{ selectedItems.length }} 件商品
            </div>
            <div class="total-price">
              合计：<span class="price">¥{{ totalPrice.toFixed(2) }}</span>
            </div>
            <a-button
              type="primary"
              size="large"
              :disabled="!selectedItems.length"
              @click="checkout"
            >
              结算
            </a-button>
            <a-button
              type="primary" danger 
              size="large"
              
              @click="handleClearCart()"
            >
              清空购物车
            </a-button>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '../stores/cart'
import { useUserStore } from '../stores/user'
import { message, Modal } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const cartStore = useCartStore()
const userStore = useUserStore()

// 设置router实例到cart store，用于认证错误处理
cartStore.setRouter(router)

// 购物车商品列表（添加选中状态）
const cartItems = ref([])

// 初始化购物车数据
const initCartData = () => {
  cartItems.value = cartStore.items.map(item => ({
    ...item,
    selected: true
  }))
}

// 全选状态
const allSelected = computed({
  get: () => cartItems.value.length > 0 && cartItems.value.every(item => item.selected),
  set: (value) => {
    cartItems.value.forEach(item => item.selected = value)
  }
})

// 已选择的商品
const selectedItems = computed(() => {
  return cartItems.value.filter(item => item.selected)
})

// 总价
const totalPrice = computed(() => {
  return selectedItems.value.reduce((total, item) => {
    return total + (item.price * item.quantity)
  }, 0)
})

// 处理数量变化
const handleQuantityChange = async (cartItemId, quantity) => {
  if (quantity <= 0 || quantity > 10) return

  try {
    await cartStore.updateQuantity(cartItemId, quantity)
    // 更新本地显示
    const item = cartItems.value.find(item => item.id === cartItemId)
    if (item) {
      item.quantity = quantity
    }
    message.success('数量更新成功')
  } catch (error) {
    message.error('更新数量失败：' + error.message)
  }
}

// 处理移除商品
const handleRemoveItem = async (cartItemId) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要从购物车中移除该商品吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await cartStore.removeFromCart(cartItemId)
        // 从本地列表中移除
        const index = cartItems.value.findIndex(item => item.id === cartItemId)
        if (index > -1) {
          cartItems.value.splice(index, 1)
        }
        message.success('商品已移除')
      } catch (error) {
        message.error('移除失败：' + error.message)
      }
    }
  })
}

// 处理清空购物车
const handleClearCart = async () => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要清空购物车吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await cartStore.clearCart()
        // 从本地列表中移除
        cartItems.value = []
        message.success('商品已移除')
      } catch (error) {
        message.error('移除失败：' + error.message)
      }
    }
  })
}

// 切换全选状态
const toggleSelectAll = (e) => {
  // cartItems.value.forEach(item => item.selected = checked)
  const isChecked = e.target.checked
  cartItems.value.forEach(item => item.selected = isChecked)
}

// 更新选中状态
const updateSelection = () => {
  // 触发计算属性更新
}

// 结算
const checkout = () => {
  if (!userStore.isLoggedIn) {
    message.warning('请先登录');
    router.push('/login');
    return;
  }

  if (selectedItems.value.length === 0) {
    message.warning('请选择要结算的商品');
    return;
  }

  // 将选中的购物车项ID传递给结算页面
  router.push({ 
    name: 'Settlement', 
    query: { cartIds: selectedItems.value.map(item => item.id).join(',') } 
  });
};

// 组件挂载时检查登录状态并加载购物车数据
onMounted(async () => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    message.warning('请先登录')
    router.push({
      path: '/login',
      query: { redirect: '/cart' }
    })
    return
  }

  try {
    await cartStore.loadCartFromServer()
    initCartData()
  } catch (error) {
    console.error('加载购物车数据失败:', error)

    // 如果是401错误，说明token已过期
    if (error.response && error.response.status === 401) {
      message.warning('登录已过期，请重新登录')
      await userStore.logout()
      router.push({
        path: '/login',
        query: { redirect: '/cart' }
      })
    } else {
      message.error('加载购物车数据失败')
    }
  }
})

// 监听购物车数据变化
cartStore.$subscribe(() => {
  initCartData()
})
</script>

<style scoped>
.cart-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24px;
}

.cart-container {
  max-width: 1200px;
  margin: 0 auto;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 0;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.88);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
  gap: 16px;
}

.cart-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.cart-item {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.item-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
}

.item-image {
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  min-width: 0;
}

.item-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.88);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-spec {
  margin-bottom: 4px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.item-price {
  font-size: 16px;
  font-weight: 500;
  color: #ff4d4f;
}

.item-quantity {
  flex-shrink: 0;
}

.item-total {
  flex-shrink: 0;
  font-size: 16px;
  font-weight: 500;
  color: #ff4d4f;
  min-width: 80px;
  text-align: right;
}

.remove-button {
  flex-shrink: 0;
}

.cart-footer {
  position: sticky;
  bottom: 0;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
  border-radius: 8px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
}

.select-all {
  flex-shrink: 0;
}

.cart-summary {
  display: flex;
  align-items: center;
  gap: 24px;
}

.total-items {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
}

.total-price {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.88);
}

.price {
  color: #ff4d4f;
  font-size: 20px;
  font-weight: 600;
}

@media (max-width: 768px) {
  .cart-page {
    padding: 16px;
  }

  .item-content {
    flex-wrap: wrap;
    gap: 12px;
  }

  .item-info {
    flex: 1 1 100%;
    order: 2;
  }

  .item-quantity {
    order: 3;
  }

  .item-total {
    order: 4;
  }

  .remove-button {
    order: 5;
  }

  .cart-footer {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .cart-summary {
    justify-content: space-between;
  }
}
</style>