import axios from 'axios'
import { processApiResponseIds, COMMON_ID_FIELDS } from '../utils/bigint'
import { STATUS_CODES, isAuthError } from '../utils/auth'

const BASE_URL = 'http://localhost:8000'

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  withCredentials: true // 支持跨域携带cookie
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 添加token认证信息
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    const { data } = response
    // 统一处理响应格式
    if (data.code === STATUS_CODES.SUCCESS) {
      console.log('API原始响应数据:', data)
      // 暂时禁用processApiResponseIds，直接返回data来调试
      // const processedData = processApiResponseIds(data, COMMON_ID_FIELDS)
      // console.log('处理后的响应数据:', processedData)
      return data
    } else if (data.code === STATUS_CODES.NOT_LOGIN) {
      // 处理业务层面的未登录错误
      const authError = new Error(data.message || '未登录')
      authError.response = {
        status: STATUS_CODES.UNAUTHORIZED,
        data: { code: STATUS_CODES.NOT_LOGIN, message: data.message || '未登录' }
      }
      throw authError
    } else {
      throw new Error(data.message || '请求失败')
    }
  },
  error => {
    console.error('用户API请求错误:', error)

    // 检查是否为认证错误（HTTP 401 或业务状态码 1009）
    if (isAuthError(error)) {
      // 清除本地token（但不直接跳转，让调用方处理）
      localStorage.removeItem('token')
      localStorage.removeItem('userId')
      localStorage.removeItem('userName')
      localStorage.removeItem('avatar')
      localStorage.removeItem('role')

      // 注意：不在这里直接跳转，而是让调用方通过统一的认证错误处理函数处理
    }

    throw error
  }
)

/**
 * 用户登录
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @returns {Promise<string>} 登录成功返回用户信息或token
 */
export const login = async (username, password) => {
  try {
    return await api.post('/user/login', null, {
      params: {
        username,
        password
      }
    })
  } catch (error) {
    console.error('用户登录失败:', error)
    throw error
  }
}

/**
 * 用户登出
 * @returns {Promise<void>}
 */
export const logout = async () => {
  try {
    return await api.post('/user/logout')
  } catch (error) {
    console.error('用户登出失败:', error)
    throw error
  }
}

/**
 * 用户注册
 * @param {Object} userInfo - 用户信息
 * @param {string} userInfo.userName - 用户名
 * @param {string} userInfo.password - 密码
 * @param {string} [userInfo.avatar] - 头像URL
 * @param {string} [userInfo.role] - 角色，默认为'user'
 * @returns {Promise<void>}
 */
export const register = async (userInfo) => {
  try {
    const userData = {
      userName: userInfo.userName,
      avatar: userInfo.avatar || '',
      role: userInfo.role || 'user'
    }
    return await api.post('/user/add', userData)
  } catch (error) {
    console.error('用户注册失败:', error)
    throw error
  }
}

/**
 * 获取用户信息
 * @param {string|number} userId - 用户ID（支持字符串格式以避免大整数精度丢失）
 * @returns {Promise<Object>} 用户信息
 */
export const getUserInfo = async (userId) => {
  try {
    return await api.get(`/user/detail/${userId}`)
  } catch (error) {
    console.error('获取用户信息失败:', error)
    throw error
  }
}

/**
 * 更新用户信息
 * @param {Object} userInfo - 用户信息
 * @param {string|number} userInfo.id - 用户ID（支持字符串格式以避免大整数精度丢失）
 * @param {string} [userInfo.userName] - 用户名
 * @param {string} [userInfo.avatar] - 头像URL
 * @param {string} [userInfo.role] - 角色
 * @returns {Promise<void>}
 */
export const updateUserInfo = async (userInfo) => {
  try {
    return await api.put('/user/update', userInfo)
  } catch (error) {
    console.error('更新用户信息失败:', error)
    throw error
  }
}

/**
 * 删除用户
 * @param {number} userId - 用户ID
 * @returns {Promise<void>}
 */
export const deleteUser = async (userId) => {
  try {
    return await api.delete(`/user/delete/${userId}`)
  } catch (error) {
    console.error('删除用户失败:', error)
    throw error
  }
}

/**
 * 获取用户地址列表
 * @returns {Promise}
 */
export const apiGetAddressList = () => {
  return request({
    url: '/address/list',
    method: 'GET',
  });
};
