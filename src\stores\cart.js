import { defineStore } from 'pinia'
import { addToCart as apiAddToCart, removeFromCart as apiRemoveFromCart, getCartList, updateCartItemQuantity, clearCart as apiClearCart, getSettleProductInfo as apiGetSettleProductInfo, submitOrder as apiSubmitOrder } from '../api/cart'
import { useUserStore } from './user'
import { requireAuth, handleCartAuthError, isUserLoggedIn } from '../utils/auth'

export const useCartStore = defineStore('cart', {
  state: () => ({
    items: [],
    loading: false,
    syncing: false, // 同步状态
    router: null, // 存储router实例用于认证错误处理
    selectedCartIds: [] // 存储选中的购物车项ID，用于结算
  }),

  getters: {
    totalPrice: (state) => {
      return state.items.reduce((total, item) => {
        return total + (item.price * item.quantity)
      }, 0)
    },
    totalQuantity: (state) => {
      return state.items.reduce((total, item) => total + item.quantity, 0)
    },
    totalItems: (state) => {
      return state.items.reduce((total, item) => total + item.quantity, 0)
    },
    // 获取购物车商品数量（用于显示徽章）
    cartItemCount: (state) => {
      return state.items.length
    }
  },

  actions: {
    // 设置router实例
    setRouter(router) {
      this.router = router
    },

    // 从服务器加载购物车数据
    async loadCartFromServer() {
      const userStore = useUserStore()
      if (!userStore.isLoggedIn) {
        return
      }

      try {
        this.loading = true
        const cartData = await getCartList()
        this.items = cartData.data || []
      } catch (error) {
        console.error('加载购物车数据失败:', error)

        // 使用统一的认证错误处理
        if (this.router) {
          try {
            await handleCartAuthError(error, this.router, '加载购物车')
            return // 如果是认证错误，已经处理了重定向，直接返回
          } catch (authError) {
            // 如果不是认证错误，继续抛出原错误
          }
        }

        throw error
      } finally {
        this.loading = false
      }
    },

    // 添加商品到购物车
    async addToCart(product, quantity = 1, specModel = '') {
      const userStore = useUserStore()

      // 检查认证状态 - 必须登录才能添加到购物车
      if (!isUserLoggedIn(userStore)) {
        if (this.router) {
          const isLoggedIn = await requireAuth(this.router, userStore)
          if (!isLoggedIn) {
            return // 已经重定向到登录页
          }
        } else {
          throw new Error('请先登录')
        }
      }

      try {
        this.syncing = true

        // 用户已登录，添加到服务器
        const cartItem = {
          userId: userStore.userId, // 保持字符串格式，避免精度丢失
          productId: product.id,
          specModel: specModel,
          quantity: quantity
        }

        await apiAddToCart(cartItem)
        // 重新加载购物车数据
        await this.loadCartFromServer()
      } catch (error) {
        console.error('添加到购物车失败:', error)

        // 使用统一的认证错误处理
        if (this.router) {
          try {
            await handleCartAuthError(error, this.router, '添加到购物车')
            return // 如果是认证错误，已经处理了重定向，直接返回
          } catch (authError) {
            // 如果不是认证错误，继续抛出原错误
          }
        }

        throw error
      } finally {
        this.syncing = false
      }
    },

    // 从购物车移除商品
    async removeFromCart(cartItemId) {
      const userStore = useUserStore()

      // 检查认证状态 - 必须登录才能操作购物车
      if (!isUserLoggedIn(userStore)) {
        if (this.router) {
          const isLoggedIn = await requireAuth(this.router, userStore)
          if (!isLoggedIn) {
            return // 已经重定向到登录页
          }
        } else {
          throw new Error('请先登录')
        }
      }

      try {
        this.syncing = true

        // 用户已登录，从服务器删除
        await apiRemoveFromCart(cartItemId)
        // 重新加载购物车数据
        await this.loadCartFromServer()
      } catch (error) {
        console.error('从购物车移除商品失败:', error)

        // 使用统一的认证错误处理
        if (this.router) {
          try {
            await handleCartAuthError(error, this.router, '移除购物车商品')
            return // 如果是认证错误，已经处理了重定向，直接返回
          } catch (authError) {
            // 如果不是认证错误，继续抛出原错误
          }
        }

        throw error
      } finally {
        this.syncing = false
      }
    },

    // 更新商品数量
    async updateQuantity(cartItemId, quantity) {
      if (quantity <= 0 || quantity > 10) return

      const userStore = useUserStore()

      // 检查认证状态 - 必须登录才能操作购物车
      if (!isUserLoggedIn(userStore)) {
        if (this.router) {
          const isLoggedIn = await requireAuth(this.router, userStore)
          if (!isLoggedIn) {
            return // 已经重定向到登录页
          }
        } else {
          throw new Error('请先登录')
        }
      }

      try {
        this.syncing = true

        // 找到对应的购物车项
        const cartItem = this.items.find(item => item.id === cartItemId)
        if (cartItem) {
          const newCartItem = {
            userId: userStore.userId, // 保持字符串格式，避免精度丢失
            productId: cartItem.productId,
            specModel: cartItem.specModel,
            quantity: quantity
          }

          await updateCartItemQuantity(cartItemId, newCartItem)
          // 重新加载购物车数据
          await this.loadCartFromServer()
        }
      } catch (error) {
        console.error('更新商品数量失败:', error)

        // 使用统一的认证错误处理
        if (this.router) {
          try {
            await handleCartAuthError(error, this.router, '更新商品数量')
            return // 如果是认证错误，已经处理了重定向，直接返回
          } catch (authError) {
            // 如果不是认证错误，继续抛出原错误
          }
        }

        throw error
      } finally {
        this.syncing = false
      }
    },

    // 清空购物车
    async clearCart() {
      const userStore = useUserStore()

      // 检查认证状态 - 必须登录才能操作购物车
      if (!isUserLoggedIn(userStore)) {
        if (this.router) {
          const isLoggedIn = await requireAuth(this.router, userStore)
          if (!isLoggedIn) {
            return // 已经重定向到登录页
          }
        } else {
          throw new Error('请先登录')
        }
      }

      try {
        this.syncing = true

        // 用户已登录，清空服务器购物车
        // const cartItemIds = this.items.map(item => item.id)
        // if (cartItemIds.length > 0) {
        //   await apiClearCart()
        // }
        await apiClearCart()
        // 重新加载购物车数据（应该为空）
        await this.loadCartFromServer()
      } catch (error) {
        console.error('清空购物车失败:', error)

        // 使用统一的认证错误处理
        if (this.router) {
          try {
            await handleCartAuthError(error, this.router, '清空购物车')
            return // 如果是认证错误，已经处理了重定向，直接返回
          } catch (authError) {
            // 如果不是认证错误，继续抛出原错误
          }
        }

        throw error
      } finally {
        this.syncing = false
      }
    },

    // 用户登录后清空本地状态并从服务器加载数据
    async initCartAfterLogin() {
      const userStore = useUserStore()
      if (!userStore.isLoggedIn) {
        // 用户未登录，清空购物车
        this.items = []
        return
      }

      try {
        this.loading = true
        // 从服务器加载购物车数据
        await this.loadCartFromServer()
      } catch (error) {
        console.error('初始化购物车失败:', error)
        // 如果加载失败，清空本地数据
        this.items = []
        throw error
      } finally {
        this.loading = false
      }
    },

    // 用户登出后清空购物车数据
    clearCartOnLogout() {
      this.items = []
      this.loading = false
      this.syncing = false
    },

    // 设置选中的购物车项ID
    setSelectedCartIds(cartIds) {
      this.selectedCartIds = cartIds
    },

    // 获取结算商品信息
    async getSettleProductInfo(cartIds) {
      try {
        const response = await apiGetSettleProductInfo({ cartIds });
        return response;
      } catch (error) {
        console.error('获取结算商品信息失败:', error);
        throw error;
      }
    },

    // 提交订单
    async submitOrder(orderData) {
      const userStore = useUserStore()

      // 检查认证状态
      if (!isUserLoggedIn(userStore)) {
        if (this.router) {
          const isLoggedIn = await requireAuth(this.router, userStore)
          if (!isLoggedIn) {
            return // 已经重定向到登录页
          }
        } else {
          throw new Error('请先登录')
        }
      }

      try {
        this.syncing = true
        const response = await apiSubmitOrder(orderData);
        return response;
      } catch (error) {
        console.error('提交订单失败:', error);

        // 使用统一的认证错误处理
        if (this.router) {
          try {
            await handleCartAuthError(error, this.router, '提交订单')
            return // 如果是认证错误，已经处理了重定向，直接返回
          } catch (authError) {
            // 如果不是认证错误，继续抛出原错误
          }
        }

        throw error;
      } finally {
        this.syncing = false
      }
    }
  }
})