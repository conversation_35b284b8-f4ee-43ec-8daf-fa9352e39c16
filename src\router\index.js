import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../stores/user'
import { handleAuthError, isUserLoggedIn } from '../utils/auth'
import { message } from 'ant-design-vue'
import Home from '../views/Home.vue'
import ProductDetail from '../views/ProductDetail.vue'
import ProductList from '../views/ProductList.vue'
import Cart from '../views/Cart.vue'
import Login from '../views/Login.vue'
import Profile from '../views/Profile.vue'
import Debug from '../views/Debug.vue'
import Settlement from "@/views/Settlement.vue";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home
    },
    {
      path: '/product/list',
      name: 'product-list',
      component: ProductList
    },
    {
      path: '/product/:id',
      name: 'product-detail',
      component: ProductDetail
    },
    {
      path: '/cart',
      name: 'cart',
      component: Cart,
      meta: { requiresAuth: true }
    },
    {
      path: '/settlement',
      name: 'Settlement',
      component: Settlement // 新增路由配置
    },
    {
      path: '/login',
      name: 'login',
      component: Login
    },
    {
      path: '/profile',
      name: 'profile',
      component: Profile,
      meta: { requiresAuth: true }
    },
    {
      path: '/debug',
      name: 'debug',
      component: Debug
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()

  // 如果需要登录且未登录，重定向到登录页
  if (to.meta.requiresAuth && !isUserLoggedIn(userStore)) {
    message.warning('请先登录')
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
    return
  }

  // 如果已登录，验证token有效性
  if (isUserLoggedIn(userStore)) {
    try {
      // 尝试获取用户信息来验证token是否有效
      await userStore.getCurrentUserInfo()
      next()
    } catch (error) {
      // token无效或过期，使用统一的认证错误处理
      console.error('Token验证失败:', error)

      try {
        // 使用统一的认证错误处理函数
        await handleAuthError(error, router, to.fullPath, {
          messageText: '登录已过期，请重新登录',
          showMessage: true,
          clearAuth: true
        })
        // handleAuthError会处理重定向，这里不需要调用next()
        return
      } catch (authError) {
        // 如果不是认证错误，按原逻辑处理
        await userStore.logout()

        if (to.meta.requiresAuth) {
          next({
            path: '/login',
            query: { redirect: to.fullPath }
          })
        } else {
          next()
        }
      }
    }
  } else {
    next()
  }
})

export default router