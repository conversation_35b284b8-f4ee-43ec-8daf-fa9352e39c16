<template>
  <a-modal
    v-model:open="visible"
    title="添加收货地址"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :width="600"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      class="address-form"
    >
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="收件人姓名" name="name">
            <a-input 
              v-model:value="formData.name" 
              placeholder="请输入收件人姓名"
              :maxlength="20"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="手机号码" name="phone">
            <a-input 
              v-model:value="formData.phone" 
              placeholder="请输入手机号码"
              :maxlength="11"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="省份" name="province">
            <a-select 
              v-model:value="formData.province" 
              placeholder="请选择省份"
              @change="handleProvinceChange"
            >
              <a-select-option 
                v-for="province in provinces" 
                :key="province" 
                :value="province"
              >
                {{ province }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="城市" name="city">
            <a-select 
              v-model:value="formData.city" 
              placeholder="请选择城市"
              @change="handleCityChange"
            >
              <a-select-option 
                v-for="city in cities" 
                :key="city" 
                :value="city"
              >
                {{ city }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="区/县" name="district">
            <a-select 
              v-model:value="formData.district" 
              placeholder="请选择区/县"
            >
              <a-select-option 
                v-for="district in districts" 
                :key="district" 
                :value="district"
              >
                {{ district }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="详细地址" name="detailAddress">
        <a-textarea 
          v-model:value="formData.detailAddress" 
          placeholder="请输入详细地址（街道、门牌号等）"
          :rows="3"
          :maxlength="100"
          show-count
        />
      </a-form-item>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="邮政编码" name="postalCode">
            <a-input 
              v-model:value="formData.postalCode" 
              placeholder="请输入邮政编码（选填）"
              :maxlength="6"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item>
            <a-checkbox v-model:checked="formData.isDefault">
              设为默认地址
            </a-checkbox>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useUserStore } from '../stores/user'
import { message } from 'ant-design-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'success'])

const userStore = useUserStore()
const formRef = ref()
const loading = ref(false)

// 表单数据
const formData = reactive({
  name: '',
  phone: '',
  province: '',
  city: '',
  district: '',
  detailAddress: '',
  postalCode: '',
  isDefault: false
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入收件人姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  province: [
    { required: true, message: '请选择省份', trigger: 'change' }
  ],
  city: [
    { required: true, message: '请选择城市', trigger: 'change' }
  ],
  district: [
    { required: true, message: '请选择区/县', trigger: 'change' }
  ],
  detailAddress: [
    { required: true, message: '请输入详细地址', trigger: 'blur' },
    { min: 5, max: 100, message: '详细地址长度应在5-100个字符之间', trigger: 'blur' }
  ],
  postalCode: [
    { pattern: /^\d{6}$/, message: '邮政编码应为6位数字', trigger: 'blur' }
  ]
}

// 省市区数据（简化版，实际项目中应该从API获取）
const provinces = ref([
  '北京市', '上海市', '天津市', '重庆市',
  '河北省', '山西省', '辽宁省', '吉林省', '黑龙江省',
  '江苏省', '浙江省', '安徽省', '福建省', '江西省', '山东省',
  '河南省', '湖北省', '湖南省', '广东省', '海南省',
  '四川省', '贵州省', '云南省', '陕西省', '甘肃省', '青海省',
  '内蒙古自治区', '广西壮族自治区', '西藏自治区', '宁夏回族自治区', '新疆维吾尔自治区'
])

const cities = ref([])
const districts = ref([])

// 省市区联动数据（简化版）
const cityData = {
  '北京市': ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区'],
  '上海市': ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区', '闵行区', '宝山区', '嘉定区', '浦东新区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区'],
  '广东省': ['广州市', '深圳市', '珠海市', '汕头市', '佛山市', '韶关市', '湛江市', '肇庆市', '江门市', '茂名市', '惠州市', '梅州市', '汕尾市', '河源市', '阳江市', '清远市', '东莞市', '中山市', '潮州市', '揭阳市', '云浮市']
}

const districtData = {
  '广州市': ['荔湾区', '越秀区', '海珠区', '天河区', '白云区', '黄埔区', '番禺区', '花都区', '南沙区', '从化区', '增城区'],
  '深圳市': ['罗湖区', '福田区', '南山区', '宝安区', '龙岗区', '盐田区', '龙华区', '坪山区', '光明区', '大鹏新区']
}

// 处理省份变化
const handleProvinceChange = (value) => {
  formData.city = ''
  formData.district = ''
  
  if (value === '北京市' || value === '上海市') {
    cities.value = []
    districts.value = cityData[value] || []
  } else {
    cities.value = cityData[value] || []
    districts.value = []
  }
}

// 处理城市变化
const handleCityChange = (value) => {
  formData.district = ''
  districts.value = districtData[value] || []
}

// 计算属性：弹窗可见性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    phone: '',
    province: '',
    city: '',
    district: '',
    detailAddress: '',
    postalCode: '',
    isDefault: false
  })
  cities.value = []
  districts.value = []
  formRef.value?.resetFields()
}

// 处理提交
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true

    const addressData = {
      name: formData.name,
      phone: formData.phone,
      province: formData.province,
      city: formData.city,
      district: formData.district,
      detailAddress: formData.detailAddress,
      postalCode: formData.postalCode,
      isDefault: formData.isDefault ? 1 : 0
    }

    await userStore.addAddress(addressData)
    
    message.success('地址添加成功')
    emit('success')
    visible.value = false
    resetForm()
  } catch (error) {
    console.error('添加地址失败:', error)
    if (error.message) {
      message.error(error.message)
    } else {
      message.error('添加地址失败，请稍后再试')
    }
  } finally {
    loading.value = false
  }
}

// 处理取消
const handleCancel = () => {
  visible.value = false
  resetForm()
}

// 监听弹窗关闭，重置表单
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})
</script>

<style scoped>
.address-form {
  padding-top: 16px;
}

.address-form .ant-form-item {
  margin-bottom: 16px;
}

.address-form .ant-form-item:last-child {
  margin-bottom: 0;
}
</style>
