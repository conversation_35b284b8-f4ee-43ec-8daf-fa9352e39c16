<template>
  <div class="settlement-page">
    <div class="settlement-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">确认订单</h1>
        <a-button @click="$router.back()" type="text">
          <template #icon>
            <ArrowLeftOutlined />
          </template>
          返回购物车
        </a-button>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <a-spin size="large" />
        <p>加载中...</p>
      </div>

      <template v-else>
        <!-- 收货地址选择 -->
        <a-card class="section-card" title="收货地址">
          <template #extra>
            <a-button type="primary" @click="showAddAddressModal = true">
              <template #icon>
                <PlusOutlined />
              </template>
              使用新地址
            </a-button>
          </template>

          <div v-if="addresses.length === 0" class="empty-address">
            <a-empty description="暂无收货地址">
              <template #extra>
                <a-button type="primary" @click="showAddAddressModal = true">
                  添加收货地址
                </a-button>
              </template>
            </a-empty>
          </div>

          <a-radio-group v-else v-model:value="selectedAddressId" class="address-list">
            <div v-for="address in addresses" :key="address.id" class="address-item">
              <a-radio :value="address.id" class="address-radio">
                <div class="address-content">
                  <div class="address-info">
                    <span class="recipient-name">{{ address.name }}</span>
                    <span class="recipient-phone">{{ address.phone }}</span>
                    <a-tag v-if="address.isDefault" color="orange" class="default-tag">默认</a-tag>
                  </div>
                  <div class="address-detail">
                    {{ address.fullAddress }}
                  </div>
                </div>
              </a-radio>
            </div>
          </a-radio-group>
        </a-card>

        <!-- 确认订单信息 -->
        <a-card class="section-card" title="确认订单信息">
          <div v-if="orderItems.length === 0" class="empty-order">
            <a-empty description="暂无商品信息" />
          </div>

          <div v-else class="order-items">
            <div v-for="item in orderItems" :key="item.cartId" class="order-item">
              <div class="item-image">
                <a-image
                  :src="item.productImage"
                  :width="80"
                  :height="80"
                  :preview="false"
                  fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                />
              </div>
              <div class="item-details">
                <h3 class="item-name">{{ item.productName }}</h3>
                <p class="item-description">{{ item.description }}</p>
                <div class="item-spec" v-if="item.specModel">规格：{{ item.specModel }}</div>
                <div class="item-quantity">数量：{{ item.quantity }}</div>
                <div class="item-price">
                  <span class="original-price" v-if="item.originalPrice !== item.discountPrice">
                    ¥{{ item.originalPrice.toFixed(2) }}
                  </span>
                  <span class="discount-price">¥{{ item.discountPrice.toFixed(2) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 订单备注 -->
          <div class="order-remark">
            <a-textarea
              v-model:value="orderRemark"
              placeholder="订单备注（选填）"
              :rows="3"
              :maxlength="200"
              show-count
            />
          </div>
        </a-card>

        <!-- 付款详情 -->
        <a-card class="section-card" title="付款详情">
          <div class="payment-summary">
            <div class="summary-row">
              <span class="label">商品总价：</span>
              <span class="value">¥{{ originalTotalAmount.toFixed(2) }}</span>
            </div>
            <div class="summary-row discount-row">
              <span class="label">共减金额：</span>
              <span class="value discount">-¥{{ discountAmount.toFixed(2) }}</span>
            </div>
            <div class="summary-row total-row">
              <span class="label">实付款：</span>
              <span class="value total">¥{{ payableAmount.toFixed(2) }}</span>
            </div>
          </div>

          <div class="payment-method">
            <h4>支付方式</h4>
            <a-radio-group v-model:value="paymentMethod" class="payment-options">
              <a-radio value="AliPay" class="payment-option">
                <div class="payment-content">
                  <span class="payment-icon">💰</span>
                  <span class="payment-name">支付宝</span>
                </div>
              </a-radio>
              <a-radio value="WechatPay" class="payment-option">
                <div class="payment-content">
                  <span class="payment-icon">💚</span>
                  <span class="payment-name">微信支付</span>
                </div>
              </a-radio>
            </a-radio-group>
          </div>

          <div class="submit-section">
            <a-button
              type="primary"
              size="large"
              :loading="submitting"
              :disabled="!selectedAddressId || orderItems.length === 0"
              @click="submitOrder"
              class="submit-button"
            >
              提交订单
            </a-button>
          </div>
        </a-card>
      </template>

      <!-- 添加地址弹窗 -->
      <AddAddressModal
        v-model:visible="showAddAddressModal"
        @success="handleAddressAdded"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useCartStore } from '../stores/cart'
import { useUserStore } from '../stores/user'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'
import AddAddressModal from '../components/AddAddressModal.vue'

const router = useRouter()
const route = useRoute()
const cartStore = useCartStore()
const userStore = useUserStore()

// 页面状态
const loading = ref(true)
const submitting = ref(false)

// 地址相关
const addresses = ref([])
const selectedAddressId = ref(null)
const showAddAddressModal = ref(false)

// 订单相关
const orderItems = ref([])
const orderRemark = ref('')
const paymentMethod = ref('AliPay')

// 从路由参数获取购物车ID
const getCartIdsFromRoute = () => {
  const cartIdsParam = route.query.cartIds
  if (cartIdsParam) {
    return cartIdsParam.split(',').map(id => id.trim()).filter(id => id)
  }
  return []
}

// 加载地址列表
const loadAddresses = async () => {
  try {
    const res = await userStore.getAddressList()
    addresses.value = res.data.map(address => ({
      id: address.id,
      name: address.name,
      phone: address.phone,
      fullAddress: `${address.province}${address.city}${address.district}${address.detailAddress}`,
      isDefault: address.isDefault === 1
    }))

    // 选择默认地址或第一个地址
    const defaultAddress = addresses.value.find(addr => addr.isDefault)
    if (defaultAddress) {
      selectedAddressId.value = defaultAddress.id
    } else if (addresses.value.length > 0) {
      selectedAddressId.value = addresses.value[0].id
    }
  } catch (error) {
    console.error('加载地址失败:', error)
    message.error('加载地址失败')
  }
}

// 加载订单商品信息
const loadOrderItems = async () => {
  try {
    const cartIds = getCartIdsFromRoute()
    if (cartIds.length === 0) {
      message.warning('没有选择商品，请返回购物车重新选择')
      router.push('/cart')
      return
    }

    // 设置选中的购物车ID到store
    cartStore.setSelectedCartIds(cartIds)

    const res = await cartStore.getSettleProductInfo(cartIds)
    orderItems.value = res.data || []
  } catch (error) {
    console.error('加载订单商品失败:', error)
    message.error('加载商品信息失败')
  }
}

// 计算价格
const originalTotalAmount = computed(() => {
  return orderItems.value.reduce((total, item) => {
    return total + (item.originalPrice * item.quantity)
  }, 0)
})

const totalAmount = computed(() => {
  return orderItems.value.reduce((total, item) => {
    return total + (item.discountPrice * item.quantity)
  }, 0)
})

const discountAmount = computed(() => {
  return originalTotalAmount.value - totalAmount.value
})

const payableAmount = computed(() => {
  return totalAmount.value
})

// 处理地址添加成功
const handleAddressAdded = () => {
  loadAddresses()
  message.success('地址添加成功')
}

// 提交订单
const submitOrder = async () => {
  if (!selectedAddressId.value) {
    message.warning('请选择收货地址')
    return
  }

  if (orderItems.value.length === 0) {
    message.warning('没有可结算的商品')
    return
  }

  try {
    submitting.value = true

    const orderData = {
      addressId: selectedAddressId.value,
      cartIds: getCartIdsFromRoute(),
      remark: orderRemark.value,
      paymentMethod: paymentMethod.value
    }

    const res = await cartStore.submitOrder(orderData)

    if (res.code === 200) {
      message.success('订单提交成功')
      // 清空购物车并跳转到订单详情页或首页
      await cartStore.clearCart()
      router.push('/')
    } else {
      message.error(`订单提交失败: ${res.message}`)
    }
  } catch (error) {
    console.error('订单提交失败:', error)
    message.error('订单提交失败，请稍后再试')
  } finally {
    submitting.value = false
  }
}

// 页面初始化
onMounted(async () => {
  try {
    loading.value = true
    await Promise.all([
      loadAddresses(),
      loadOrderItems()
    ])
  } catch (error) {
    console.error('页面初始化失败:', error)
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.settlement-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24px;
}

.settlement-container {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 0;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.88);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
  gap: 16px;
}

.section-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 地址选择样式 */
.empty-address {
  padding: 40px 0;
}

.address-list {
  width: 100%;
}

.address-item {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s;
}

.address-item:hover {
  border-color: #1677ff;
  background-color: #f6f9ff;
}

.address-radio {
  width: 100%;
}

.address-content {
  margin-left: 8px;
}

.address-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.recipient-name {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.88);
}

.recipient-phone {
  color: rgba(0, 0, 0, 0.65);
}

.default-tag {
  font-size: 12px;
}

.address-detail {
  color: rgba(0, 0, 0, 0.65);
  line-height: 1.5;
}

/* 订单商品样式 */
.empty-order {
  padding: 40px 0;
}

.order-items {
  margin-bottom: 24px;
}

.order-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 16px;
  background-color: #fff;
}

.item-image {
  flex-shrink: 0;
}

.item-details {
  flex: 1;
  min-width: 0;
}

.item-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.88);
}

.item-description {
  margin: 0 0 8px 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
}

.item-spec,
.item-quantity {
  margin-bottom: 4px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.item-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.original-price {
  color: rgba(0, 0, 0, 0.45);
  text-decoration: line-through;
  font-size: 14px;
}

.discount-price {
  color: #ff4d4f;
  font-size: 16px;
  font-weight: 500;
}

.order-remark {
  margin-top: 24px;
}

/* 付款详情样式 */
.payment-summary {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 8px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-row .label {
  color: rgba(0, 0, 0, 0.65);
}

.summary-row .value {
  font-weight: 500;
}

.discount-row .value {
  color: #52c41a;
}

.total-row {
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
  font-size: 16px;
}

.total-row .value {
  color: #ff4d4f;
  font-size: 18px;
  font-weight: 600;
}

.payment-method {
  margin-bottom: 24px;
}

.payment-method h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.88);
}

.payment-options {
  width: 100%;
}

.payment-option {
  display: block;
  width: 100%;
  margin-bottom: 12px;
  padding: 12px 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s;
}

.payment-option:hover {
  border-color: #1677ff;
  background-color: #f6f9ff;
}

.payment-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.payment-icon {
  font-size: 18px;
}

.payment-name {
  font-size: 14px;
  font-weight: 500;
}

.submit-section {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.submit-button {
  min-width: 200px;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settlement-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .order-item {
    flex-direction: column;
    gap: 12px;
  }

  .item-image {
    align-self: center;
  }

  .payment-option {
    margin-bottom: 8px;
  }

  .submit-button {
    width: 100%;
  }
}
</style>