<template>
  <div class="settlement-page">
    <!-- 确认收货地址 -->
    <a-card title="确认收货地址" style="margin-bottom: 20px;">
      <div v-for="address in addresses" :key="address.id" class="address-item">
        <a-radio-group v-model:value="selectedAddressId">
          <a-radio-button :value="address.id">{{ address.name }} {{ address.phone }} {{ address.fullAddress }}</a-radio-button>
        </a-radio-group>
      </div>
      <a-button type="primary" @click="showAddAddressModal">使用新地址</a-button>
    </a-card>

    <!-- 确认订单信息 -->
    <a-card title="确认订单信息" style="margin-bottom: 20px;">
      <a-table :columns="orderColumns" :data-source="orderItems" :pagination="false">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'quantity'">
            <a-input-number v-model:value="record.quantity" :min="1" />
          </template>
        </template>
      </a-table>
      <a-textarea v-model:value="orderRemark" placeholder="订单备注" :rows="4" style="margin-top: 20px;" />
    </a-card>

    <!-- 付款详情 -->
    <a-card title="付款详情">
      <p>商品总价: ¥{{ totalAmount }}</p>
      <p>共减: ¥{{ discountAmount }}</p>
      <p>实付款: ¥{{ payableAmount }}</p>
      <a-radio-group v-model:value="paymentMethod">
        <a-radio value="AliPay">支付宝</a-radio>
        <a-radio value="WechatPay">微信支付</a-radio>
      </a-radio-group>
      <a-button type="primary" @click="submitOrder" style="margin-top: 20px;">提交订单</a-button>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useCartStore } from '../stores/cart';
import { useUserStore } from '../stores/user';
import { message, Modal } from 'ant-design-vue';

const router = useRouter();
const cartStore = useCartStore();
const userStore = useUserStore();

// 地址相关
const addresses = ref([]);
const selectedAddressId = ref(null);

const loadAddresses = async () => {
  try {
    const res = await userStore.getAddressList();
    addresses.value = res.data.map(address => ({
      id: address.id,
      name: address.name,
      phone: address.phone,
      fullAddress: `${address.province}${address.city}${address.district}${address.detailAddress}`
    }));
    if (addresses.value.length > 0) {
      selectedAddressId.value = addresses.value[0].id;
    }
  } catch (error) {
    console.error('加载地址失败:', error);
  }
};

// 订单相关
const orderItems = ref([]);
const orderRemark = ref('');

const loadOrderItems = async () => {
  try {
    const res = await cartStore.getSettleProductInfo(cartStore.selectedCartIds);
    orderItems.value = res.data.map(item => ({
      ...item,
      key: item.cartId
    }));
  } catch (error) {
    console.error('加载订单商品失败:', error);
  }
};

const totalAmount = computed(() => {
  return orderItems.value.reduce((total, item) => total + item.discountPrice * item.quantity, 0);
});

const discountAmount = computed(() => {
  return orderItems.value.reduce((total, item) => total + (item.originalPrice - item.discountPrice) * item.quantity, 0);
});

const payableAmount = computed(() => {
  return totalAmount.value - discountAmount.value;
});

const paymentMethod = ref('AliPay');

const submitOrder = async () => {
  if (!selectedAddressId.value) {
    message.warning('请选择收货地址');
    return;
  }

  try {
    const orderData = {
      addressId: selectedAddressId.value,
      cartIds: cartStore.selectedCartIds,
      remark: orderRemark.value,
      paymentMethod: paymentMethod.value
    };

    // 调用后端接口提交订单
    const res = await cartStore.submitOrder(orderData);
    if (res.code === 200) {
      message.success('订单提交成功');
      // 清空购物车并跳转到订单详情页
      await cartStore.clearCart();
      router.push(`/order/${res.data.orderId}`);
    } else {
      message.error(`订单提交失败: ${res.message}`);
    }
  } catch (error) {
    console.error('订单提交失败:', error);
    message.error('订单提交失败，请稍后再试');
  }
};

onMounted(async () => {
  await loadAddresses();
  await loadOrderItems();
});
</script>

<style scoped>
.address-item {
  margin-bottom: 10px;
}
</style>